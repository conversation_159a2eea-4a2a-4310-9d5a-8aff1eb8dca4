/*
 * This file is part of LiquidBounce (https://github.com/CCBlueX/LiquidBounce)
 *
 * Copyright (c) 2015 - 2025 CCBlueX
 *
 * LiquidBounce is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * LiquidBounce is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with LiquidBounce. If not, see <https://www.gnu.org/licenses/>.
 */

package net.ccbluex.liquidbounce.features.module.modules.player.autoqueue.trigger

import net.ccbluex.liquidbounce.event.events.TitleEvent
import net.ccbluex.liquidbounce.event.handler

object AutoQueueTriggerTitle : AutoQueueTrigger("Title") {
    override var isTriggered: Boolean = false
        get() = field.apply { field = false }

    private val keywords by textList("Keywords", mutableListOf("Win", "胜利"))

    @Suppress("unused")
    private val titleHandler = handler<TitleEvent.Title> { event ->
        val string = event.text?.string ?: return@handler
        if (keywords.any { string.contains(it) }) {
            isTriggered = true
        }
    }
}
