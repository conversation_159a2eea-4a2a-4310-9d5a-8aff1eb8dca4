/*
 * This file is part of LiquidBounce (https://github.com/CCBlueX/LiquidBounce)
 *
 * Copyright (c) 2015 - 2025 CCBlueX
 *
 * LiquidBounce is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * LiquidBounce is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with LiquidBounce. If not, see <https://www.gnu.org/licenses/>.
 */
package net.ccbluex.liquidbounce.features.module.modules.player

import net.ccbluex.liquidbounce.config.types.NamedChoice
import net.ccbluex.liquidbounce.event.events.NotificationEvent
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.ClientModule
import net.ccbluex.liquidbounce.injection.mixins.minecraft.gui.MixinScoreboard
import net.ccbluex.liquidbounce.utils.client.notification

/**
 * AntiExploit module
 *
 * Prevents exploits (and bugs).
 */
object ModuleAntiExploit : ClientModule("AntiExploit", Category.PLAYER) {
    private val limits = multiEnumChoice("Limit", Limit.entries)
    val cancelDemo by boolean("CancelDemo", true)
    val ignoreProtocol by boolean("IgnoreProtocolKick", true)
    val notify by boolean("Notify", true)

    /**
     * Implemented in [MixinScoreboard].
     */
    val vfpScoreboardFix by boolean("VFPScoreboardFix", false)

    fun notifyAboutExploit(message: String, crashAttempt: Boolean) {
        if (notify) {
            notification(
                "Notifier",
                message,
                if (crashAttempt) NotificationEvent.Severity.ERROR else NotificationEvent.Severity.INFO
            )
        }
    }

    @JvmStatic
    fun canLimit(limit: Limit): Boolean = running && limit in limits
}

enum class Limit(override val choiceName: String) : NamedChoice {
    EXPLOSION_STRENGTH("ExplosionStrength"),
    PARTICLES_AMOUNT("ParticlesAmount"),
    PARTICLES_SPEED("ParticlesSpeed")
}
