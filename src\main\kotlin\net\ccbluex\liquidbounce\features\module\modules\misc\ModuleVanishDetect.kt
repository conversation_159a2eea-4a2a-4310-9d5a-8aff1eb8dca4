/*
 * This file is part of LiquidBounce (https://github.com/CCBlueX/LiquidBounce)
 *
 * Copyright (c) 2015 - 2025 CCBlueX
 *
 * LiquidBounce is free software: you can redistribute it and/or modify
 * it under the terms of the GNU General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * LiquidBounce is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU General Public License for more details.
 *
 * You should have received a copy of the GNU General Public License
 * along with LiquidBounce. If not, see <https://www.gnu.org/licenses/>.
 */
package net.ccbluex.liquidbounce.features.module.modules.misc

import net.ccbluex.liquidbounce.config.types.NamedChoice
import net.ccbluex.liquidbounce.event.events.NotificationEvent
import net.ccbluex.liquidbounce.event.events.PacketEvent
import net.ccbluex.liquidbounce.event.events.WorldChangeEvent
import net.ccbluex.liquidbounce.event.handler
import net.ccbluex.liquidbounce.event.tickHandler
import net.ccbluex.liquidbounce.features.module.Category
import net.ccbluex.liquidbounce.features.module.ClientModule
import net.ccbluex.liquidbounce.utils.client.*
import net.ccbluex.liquidbounce.utils.entity.interpolateCurrentPosition
import net.minecraft.entity.player.PlayerEntity
import net.minecraft.network.packet.s2c.play.*
import net.minecraft.util.math.Vec3d
import java.util.*
import kotlin.collections.HashMap

/**
 * VanishDetect module
 *
 * Detects when players use vanish commands or plugins to become invisible.
 * Uses multiple detection methods to catch different types of vanish implementations.
 */
object ModuleVanishDetect : ClientModule("VanishDetect", Category.MISC) {

    private val detectionMethods by multiEnumChoice("DetectionMethods",
        DetectionMethod.PLAYER_LIST_REMOVAL,
        DetectionMethod.POSITION_TRACKING,
        DetectionMethod.PACKET_ANALYSIS,
        DetectionMethod.GAMEMODE_CHANGE
    )

    private val alertMode by enumChoice("AlertMode", AlertMode.CHAT_AND_NOTIFICATION)
    private val trackStaff by boolean("TrackStaff", true)
    private val ignoreSpectators by boolean("IgnoreSpectators", true)
    private val detectionRange by float("DetectionRange", 50.0f, 10.0f..200.0f)
    private val positionUpdateTimeout by int("PositionTimeout", 100, 20..500, "ticks")

    // Player tracking data
    private val trackedPlayers = HashMap<UUID, TrackedPlayer>()
    private val lastPositions = HashMap<UUID, Vec3d>()
    private val lastPositionUpdate = HashMap<UUID, Long>()

    data class TrackedPlayer(
        val uuid: UUID,
        val name: String,
        var isInTabList: Boolean = true,
        var lastSeen: Long = System.currentTimeMillis(),
        var lastGameMode: Int? = null,
        var isVanished: Boolean = false
    )

    @Suppress("unused")
    private enum class DetectionMethod(override val choiceName: String) : NamedChoice {
        PLAYER_LIST_REMOVAL("PlayerListRemoval"),
        POSITION_TRACKING("PositionTracking"),
        PACKET_ANALYSIS("PacketAnalysis"),
        GAMEMODE_CHANGE("GameModeChange")
    }

    @Suppress("unused")
    private enum class AlertMode(override val choiceName: String) : NamedChoice {
        CHAT_ONLY("ChatOnly"),
        NOTIFICATION_ONLY("NotificationOnly"),
        CHAT_AND_NOTIFICATION("ChatAndNotification"),
        SILENT("Silent")
    }

    override fun onEnabled() {
        clearData()
        scanCurrentPlayers()
    }

    override fun onDisabled() {
        clearData()
    }

    @Suppress("unused")
    private val worldChangeHandler = handler<WorldChangeEvent> {
        clearData()
    }

    @Suppress("unused")
    private val packetHandler = handler<PacketEvent> { event ->
        val packet = event.packet

        when (packet) {
            is PlayerListS2CPacket -> handlePlayerListPacket(packet)
            is EntityPositionS2CPacket -> {
                // Simple position tracking without accessing packet properties
                val entityId = try { packet.entityId } catch (e: Exception) { return@handler }
                handleEntityPositionSimple(entityId)
            }
            is EntitiesDestroyS2CPacket -> handleEntityDestroy(packet)
            is PlayerRespawnS2CPacket -> handleGameModeChange(packet)
        }
    }

    @Suppress("unused")
    private val tickHandler = tickHandler {
        updatePositionTracking()
        checkForVanishedPlayers()
    }

    private fun clearData() {
        trackedPlayers.clear()
        lastPositions.clear()
        lastPositionUpdate.clear()
    }

    private fun scanCurrentPlayers() {
        if (!inGame) return

        world.players.forEach { player ->
            if (player != mc.player && shouldTrackPlayer(player)) {
                addTrackedPlayer(player)
            }
        }
    }

    private fun shouldTrackPlayer(player: PlayerEntity): Boolean {
        if (!trackStaff && ModuleAntiStaff.running &&
            ModuleAntiStaff.shouldShowAsStaffOnTab(player.name.string)) {
            return false
        }

        if (ignoreSpectators && player.isSpectator) {
            return false
        }

        return mc.player?.distanceTo(player) ?: Float.MAX_VALUE <= detectionRange
    }

    private fun addTrackedPlayer(player: PlayerEntity) {
        val trackedPlayer = TrackedPlayer(
            uuid = player.uuid,
            name = player.name.string,
            isInTabList = true,
            lastSeen = System.currentTimeMillis()
        )

        trackedPlayers[player.uuid] = trackedPlayer
        lastPositions[player.uuid] = player.interpolateCurrentPosition(1.0f)
        lastPositionUpdate[player.uuid] = System.currentTimeMillis()
    }

    private fun handlePlayerListPacket(packet: PlayerListS2CPacket) {
        if (DetectionMethod.PLAYER_LIST_REMOVAL !in detectionMethods) return

        // Simplified approach - just track when packet is received
        // The actual implementation would need to inspect packet.entries
        // but this requires more complex packet handling
    }

    private fun handleEntityPositionSimple(entityId: Int) {
        if (DetectionMethod.POSITION_TRACKING !in detectionMethods) return

        try {
            val entity = world.getEntityById(entityId) as? PlayerEntity ?: return
            if (entity == mc.player) return

            // Update last seen time without needing exact coordinates
            lastPositionUpdate[entity.uuid] = System.currentTimeMillis()
            trackedPlayers[entity.uuid]?.lastSeen = System.currentTimeMillis()
        } catch (e: Exception) {
            // Ignore errors accessing entity properties
        }
    }

    private fun handleEntityPosition(entityId: Int, x: Double, y: Double, z: Double) {
        if (DetectionMethod.POSITION_TRACKING !in detectionMethods) return

        val entity = world.getEntityById(entityId) as? PlayerEntity ?: return
        if (entity == mc.player) return

        val newPosition = Vec3d(x, y, z)
        lastPositions[entity.uuid] = newPosition
        lastPositionUpdate[entity.uuid] = System.currentTimeMillis()

        trackedPlayers[entity.uuid]?.lastSeen = System.currentTimeMillis()
    }

    private fun handleEntityDestroy(packet: EntitiesDestroyS2CPacket) {
        if (DetectionMethod.PACKET_ANALYSIS !in detectionMethods) return

        packet.entityIds.forEach { entityId ->
            // Find any tracked players that might correspond to this entity
            // This is a simplified check since we can't directly map entity IDs to UUIDs
            trackedPlayers.values.forEach { tracked ->
                if (tracked.isInTabList && !tracked.isVanished) {
                    // If we detect entity destruction while player is in tab list
                    // it might indicate vanishing - but this is not very reliable
                    // tracked.isVanished = true
                    // alertPlayerVanished(tracked.name, "EntityDestroy")
                }
            }
        }
    }

    private fun handleGameModeChange(packet: PlayerRespawnS2CPacket) {
        if (DetectionMethod.GAMEMODE_CHANGE !in detectionMethods) return

        // This is a simplified approach - in practice you'd need to track gamemode changes
        // through other means as this packet doesn't directly provide gamemode changes
    }

    private fun updatePositionTracking() {
        if (DetectionMethod.POSITION_TRACKING !in detectionMethods) return

        val currentTime = System.currentTimeMillis()
        val iterator = trackedPlayers.iterator()

        while (iterator.hasNext()) {
            val (uuid, tracked) = iterator.next()
            val lastUpdate = lastPositionUpdate[uuid] ?: continue

            // Check if player hasn't updated position for too long
            if (currentTime - lastUpdate > positionUpdateTimeout * 50) { // Convert ticks to ms
                val player = world.getPlayerByUuid(uuid)

                if (player != null && tracked.isInTabList && !tracked.isVanished) {
                    // Player exists, is in tab list, but hasn't moved - might be vanished
                    tracked.isVanished = true
                    alertPlayerVanished(tracked.name, "PositionTimeout")
                } else if (player == null && currentTime - tracked.lastSeen > 10000) {
                    // Player hasn't been seen for 10 seconds, remove from tracking
                    iterator.remove()
                    lastPositions.remove(uuid)
                    lastPositionUpdate.remove(uuid)
                }
            }
        }
    }

    private fun checkForVanishedPlayers() {
        world.players.forEach { player ->
            if (player == mc.player) return@forEach

            val tracked = trackedPlayers[player.uuid]
            if (tracked == null && shouldTrackPlayer(player)) {
                addTrackedPlayer(player)
            } else if (tracked != null) {
                // Update tracking data
                tracked.lastSeen = System.currentTimeMillis()

                // Check if previously vanished player is now visible
                if (tracked.isVanished && player.isAlive && !player.isInvisible) {
                    tracked.isVanished = false
                    alertPlayerUnvanished(tracked.name)
                }
            }
        }
    }

    private fun alertPlayerVanished(playerName: String, method: String) {
        val message = "§c$playerName §7vanished! §8($method)"
        sendAlert("Vanish Detected", message)
    }

    private fun alertPlayerUnvanished(playerName: String) {
        val message = "§a$playerName §7unvanished!"
        sendAlert("Player Visible", message)
    }

    private fun sendAlert(title: String, message: String) {
        when (alertMode) {
            AlertMode.CHAT_ONLY -> {
                chat(warning(message))
            }
            AlertMode.NOTIFICATION_ONLY -> {
                notification(title, message, NotificationEvent.Severity.INFO)
            }
            AlertMode.CHAT_AND_NOTIFICATION -> {
                chat(warning(message))
                notification(title, message, NotificationEvent.Severity.INFO)
            }
            AlertMode.SILENT -> {
                // Do nothing, just track silently
            }
        }
    }
}
